import { defineStore } from 'pinia'

export const useCartStore = defineStore('cart', {
  state: () => ({
    cartList: {
      list: [],
      total: 0,
      totalprice: '0.00'
    },
    numtotal: {}, // 每个商品的总数量 {proid: num}
    numCat: {}, // 每个分类的总数量 {catid: num}
    bid: '', // 商家ID
    tableId: '' // 桌台ID
  }),

  getters: {
    // 计算购物车总数量
    cartTotal: (state) => {
      return state.cartList.list.reduce((total, item) => total + item.num, 0)
    },
    
    // 计算购物车总价格
    cartTotalPrice: (state) => {
      const total = state.cartList.list.reduce((total, item) => {
        return total + (item.num * parseFloat(item.guige.sell_price))
      }, 0)
      return total.toFixed(2)
    },
    
    // 获取商品在购物车中的数量
    getProductNum: (state) => (proid, ggid) => {
      const item = state.cartList.list.find(item => item.proid === proid && item.ggid === ggid)
      return item ? item.num : 0
    },
    
    // 获取分类的商品总数量
    getCategoryNum: (state) => (catid) => {
      return state.numCat[catid] || 0
    }
  },

  actions: {
    // 初始化购物车
    initCart(bid, tableId) {
      this.bid = bid
      this.tableId = tableId
      this.loadCartFromStorage()
    },
    
    // 从本地存储加载购物车
    loadCartFromStorage() {
      try {
        const key = `cart_${this.bid}_${this.tableId}`
        const cartData = uni.getStorageSync(key)
        if (cartData) {
          this.cartList = cartData.cartList || { list: [], total: 0, totalprice: '0.00' }
          this.numtotal = cartData.numtotal || {}
          this.numCat = cartData.numCat || {}
        }
      } catch (e) {
        console.error('加载购物车失败', e)
      }
    },
    
    // 保存购物车到本地存储
    saveCartToStorage() {
      try {
        const key = `cart_${this.bid}_${this.tableId}`
        const cartData = {
          cartList: this.cartList,
          numtotal: this.numtotal,
          numCat: this.numCat
        }
        uni.setStorageSync(key, cartData)
      } catch (e) {
        console.error('保存购物车失败', e)
      }
    },
    
    // 添加商品到购物车
    addToCart(product, guige, num = 1) {
      const proid = product.id
      const ggid = guige.id
      
      // 检查库存
      if (guige.stock <= 0) {
        uni.showToast({
          title: '库存不足',
          icon: 'none'
        })
        return false
      }
      
      // 查找购物车中是否已存在该商品
      const existingItem = this.cartList.list.find(item => 
        item.proid === proid && item.ggid === ggid
      )
      
      if (existingItem) {
        // 如果已存在，增加数量
        const newNum = existingItem.num + num
        if (newNum > guige.stock) {
          uni.showToast({
            title: '超出库存限制',
            icon: 'none'
          })
          return false
        }
        existingItem.num = newNum
      } else {
        // 如果不存在，添加新商品
        if (num > guige.stock) {
          uni.showToast({
            title: '超出库存限制',
            icon: 'none'
          })
          return false
        }
        
        this.cartList.list.push({
          proid,
          ggid,
          num,
          product: { ...product },
          guige: { ...guige }
        })
      }
      
      // 更新统计数据
      this.updateCartStats()
      this.saveCartToStorage()
      
      return true
    },
    
    // 从购物车减少商品
    removeFromCart(proid, ggid, num = 1) {
      const itemIndex = this.cartList.list.findIndex(item => 
        item.proid === proid && item.ggid === ggid
      )
      
      if (itemIndex > -1) {
        const item = this.cartList.list[itemIndex]
        item.num -= num
        
        if (item.num <= 0) {
          // 如果数量为0，移除商品
          this.cartList.list.splice(itemIndex, 1)
        }
        
        // 更新统计数据
        this.updateCartStats()
        this.saveCartToStorage()
      }
    },
    
    // 清空购物车
    clearCart() {
      this.cartList = {
        list: [],
        total: 0,
        totalprice: '0.00'
      }
      this.numtotal = {}
      this.numCat = {}
      
      this.saveCartToStorage()
    },
    
    // 更新购物车统计数据
    updateCartStats() {
      // 计算总数量
      this.cartList.total = this.cartList.list.reduce((total, item) => total + item.num, 0)
      
      // 计算总价格
      this.cartList.totalprice = this.cartList.list.reduce((total, item) => {
        return total + (item.num * parseFloat(item.guige.sell_price))
      }, 0).toFixed(2)
      
      // 计算每个商品的总数量
      this.numtotal = {}
      this.cartList.list.forEach(item => {
        this.numtotal[item.proid] = (this.numtotal[item.proid] || 0) + item.num
      })
      
      // 计算每个分类的总数量
      this.numCat = {}
      this.cartList.list.forEach(item => {
        const catId = item.product.cid || 'default'
        this.numCat[catId] = (this.numCat[catId] || 0) + item.num
      })
    },
    
    // 获取购物车数据（用于页面初始化）
    getCartData() {
      return {
        cartList: this.cartList,
        numtotal: this.numtotal,
        numCat: this.numCat
      }
    }
  }
})
